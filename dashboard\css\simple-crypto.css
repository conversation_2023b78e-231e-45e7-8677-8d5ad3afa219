/* Estilos simplificados para a seção de criptomoedas */

#crypto-section {
    margin: 20px 0;
    padding: 20px;
}

.crypto-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.crypto-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.crypto-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.crypto-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    border-radius: 50%;
}

.crypto-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.crypto-header span {
    font-size: 0.8rem;
    color: #6c757d;
}

.crypto-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 1.2rem;
    font-weight: 600;
}

.crypto-price .positive {
    color: #10b981;
}

.crypto-price .negative {
    color: #ef4444;
}

.crypto-details {
    font-size: 0.9rem;
}

.crypto-details div {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.crypto-details div span:first-child {
    color: #6c757d;
}

.crypto-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e5e7eb;
}

.crypto-footer p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.btn {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.btn:hover {
    background-color: #2563eb;
}

/* Responsividade */
@media (max-width: 768px) {
    .crypto-grid {
        grid-template-columns: 1fr;
    }
}
