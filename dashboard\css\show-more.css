/* Estilos para o botão "Mostrar Mais" */
.show-more-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 10px;
}

.show-more-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.show-more-button:hover {
    background-color: var(--primary-color-dark, #0056b3);
}

.show-more-button:disabled {
    background-color: var(--neutral-color-light, #d1d5db);
    cursor: not-allowed;
}

/* Estilos para linhas ocultas */
.asset-row.hidden {
    display: none;
}

/* Animação para novas linhas */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.asset-row.fade-in {
    animation: fadeIn 0.5s ease forwards;
}
