/* Estilos para a seção de criptomoedas */

.crypto-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.crypto-item {
    background-color: var(--card-bg-secondary, #f8f9fa);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease;
}

.crypto-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.crypto-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.crypto-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    border-radius: 50%;
}

.crypto-name {
    flex: 1;
}

.crypto-name h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.crypto-symbol {
    font-size: 0.8rem;
    color: var(--text-light, #6c757d);
}

.crypto-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 1.2rem;
    font-weight: 600;
}

.price-change {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.price-change.positive {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.price-change.negative {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.crypto-details {
    margin-bottom: 12px;
}

.crypto-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.crypto-detail .label {
    color: var(--text-light, #6c757d);
}

.crypto-chart {
    height: 40px;
    margin-top: 12px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.section-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color, #e5e7eb);
}

.section-footer p {
    color: var(--text-light, #6c757d);
    font-size: 0.9rem;
    margin: 0;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color, #3b82f6);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-color-dark, #2563eb);
}

/* Responsividade */
@media (max-width: 768px) {
    .crypto-grid {
        grid-template-columns: 1fr;
    }
    
    .section-footer {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
}
