/* New Layout CSS - Based on the provided design */

/* Light theme (default) */
:root {
    /* Updated color scheme */
    --primary-color: #3b82f6;
    --primary-light: #60a5fa;
    --primary-dark: #2563eb;

    --bg-color: #f8fafc;
    --card-bg: #ffffff;
    --sidebar-bg: #ffffff;
    --header-bg: #ffffff;

    --text-color: #1e293b;
    --text-light: #64748b;
    --text-dark: #0f172a;
    --text-white: #ffffff;

    --border-color: #e2e8f0;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;

    /* Shadows */
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --sidebar-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

/* Dark theme */
.theme-dark {
    --bg-color: #0f172a;
    --card-bg: #1e293b;
    --sidebar-bg: #1e293b;
    --header-bg: #1e293b;

    --text-color: #e2e8f0;
    --text-light: #94a3b8;
    --text-dark: #f8fafc;

    --border-color: #334155;

    /* Shadows */
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    --sidebar-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* Layout structure */
body {
    background-color: var(--bg-color);
    margin: 0;
    padding: 0;
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    display: flex;
}

/* Sidebar */
.sidebar {
    width: 240px;
    background-color: var(--sidebar-bg);
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    box-shadow: var(--sidebar-shadow);
    z-index: 100;
    padding-top: 1rem;
}

.sidebar-logo {
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.sidebar-logo h1 {
    font-size: 1.25rem;
    margin: 0;
    font-weight: 600;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 0.25rem;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: background-color 0.2s;
}

.sidebar-menu a:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.sidebar-menu a.active {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    font-weight: 500;
}

.sidebar-menu i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    width: 1.5rem;
    text-align: center;
}

/* Main content */
.dashboard-container {
    flex: 1;
    margin-left: 240px;
    padding: 2rem;
    max-width: calc(100% - 240px);
    overflow-x: hidden;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    font-size: 1.75rem;
    margin: 0 0 0.5rem 0;
    font-weight: 600;
}

.dashboard-header p {
    color: var(--text-light);
    margin: 0;
}

.main-content {
    width: 100%;
    display: block;
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.card h2 {
    font-size: 1.25rem;
    margin-top: 0;
    margin-bottom: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.75rem;
}

/* Metrics cards */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Portfolio metrics grid - ensure 4 cards on larger screens */
.portfolio-summary-card .metrics-grid {
    grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 1200px) {
    .portfolio-summary-card .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .portfolio-summary-card .metrics-grid {
        grid-template-columns: 1fr;
    }
}

.metric-card {
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.metric-title {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.metric-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.metric-value {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-dark);
}

.metric-change {
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    margin-top: auto;
}

.metric-change.positive {
    color: #10b981; /* Green */
}

.metric-change.negative {
    color: #ef4444; /* Red */
}

.metric-change i {
    margin-right: 0.25rem;
}

.metric-period {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-top: 0.25rem;
}

/* Tables */
.table-container {
    overflow-x: auto;
    margin-top: 1rem;
    width: 100%;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    table-layout: fixed;
}

#assets-table {
    min-width: 900px;
}

th, td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    font-weight: 500;
    color: var(--text-light);
    font-size: 0.875rem;
    background-color: #f8fafc;
}

tr:hover td {
    background-color: #f8fafc;
}

tr:last-child td {
    border-bottom: none;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.table-actions button {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
}

.table-actions button:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--primary-color);
}

/* Charts */
.chart-container {
    height: 300px;
    margin: 1.5rem 0;
    padding: 1rem 0;
}

.portfolio-allocation-card .chart-container,
.portfolio-performance-card .chart-container {
    height: 250px;
}

/* Chart filters */
.chart-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.chart-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-filter label {
    font-size: 0.875rem;
    color: var(--text-light);
}

.chart-filter select {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid var(--border-color);
    font-size: 0.875rem;
    background-color: white;
    color: var(--text-color);
}

/* Responsive */
@media (max-width: 1024px) {
    .sidebar {
        width: 200px;
    }

    .dashboard-container {
        margin-left: 200px;
        max-width: calc(100% - 200px);
    }
}

@media (max-width: 768px) {
    body {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        padding-top: 0;
    }

    .sidebar-menu {
        display: flex;
        overflow-x: auto;
        padding: 0.5rem;
    }

    .sidebar-menu li {
        margin-bottom: 0;
        margin-right: 0.5rem;
    }

    .sidebar-menu a {
        padding: 0.5rem 1rem;
    }

    .dashboard-container {
        margin-left: 0;
        max-width: 100%;
        padding: 1rem;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Loading overlay */
.loading-overlay {
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 1000;
}

/* Error message */
.error-message {
    z-index: 1000;
    border-top: 4px solid var(--negative-color);
}

/* Notification */
.notification-container {
    z-index: 1100;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
    color: var(--text-light);
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
}

.modal-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* Settings styles */
.settings-section {
    margin-bottom: 1.5rem;
}

.settings-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.setting-item {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}

.setting-item label {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-color);
}

.setting-item select {
    padding: 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    font-size: 0.875rem;
}

.checkbox-item {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-item label {
    margin-bottom: 0;
}

/* Footer */
.footer {
    margin-top: 2rem;
    padding: 1.5rem 0;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 0.875rem;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Section grid layout */
.section-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Badge styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-primary {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.badge-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.btn-icon {
    margin-right: 0.5rem;
}

/* Portfolio styles */
.portfolio-summary-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
    width: 100%;
}

.portfolio-summary-row .metric-card {
    flex: 1;
    min-width: 200px;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
}

.portfolio-charts-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    width: 100%;
}

.portfolio-charts-row .chart-card {
    flex: 1;
    min-width: 300px;
}

.chart-card .chart-container {
    height: 300px;
}

.assets-card {
    margin-bottom: 1.5rem;
    width: 100%;
    overflow-x: auto;
    display: block;
}

.card-header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.75rem;
}

.card-header-actions h2 {
    margin: 0;
}

@media (max-width: 768px) {
    .portfolio-summary-row {
        grid-template-columns: 1fr;
    }

    .portfolio-summary-row .metric-card {
        width: 100%;
    }

    .portfolio-charts-row {
        grid-template-columns: 1fr;
    }

    .portfolio-charts-row .chart-card {
        width: 100%;
    }

    .dashboard-container {
        margin-left: 0;
        max-width: 100%;
        padding: 1rem;
    }

    .sidebar {
        width: 100%;
        position: relative;
        height: auto;
    }

    .table-container {
        overflow-x: auto;
    }

    table {
        min-width: 800px;
    }
}

.empty-state td {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.empty-icon {
    font-size: 2.5rem;
    color: var(--text-light);
    opacity: 0.5;
    margin-bottom: 1rem;
}

.empty-message p {
    margin: 0;
    color: var(--text-dark);
    font-weight: 500;
}

.empty-description {
    color: var(--text-light) !important;
    font-weight: normal !important;
    margin-bottom: 1rem !important;
}

/* Open Finance styling */
.open-finance-card {
    margin-bottom: var(--spacing-lg);
}

.open-finance-content {
    padding: var(--spacing-md);
}

.connected-accounts {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-md);
}

.account-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-md);
    box-shadow: var(--card-shadow);
    transition: transform 0.2s, box-shadow 0.2s;
}

.account-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.account-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.account-icon {
    font-size: 1.5rem;
    margin-right: var(--spacing-md);
    color: var(--primary-color);
}

.account-info {
    flex: 1;
}

.account-name {
    font-weight: 600;
    margin: 0;
    color: var(--text-dark);
}

.account-type {
    font-size: 0.85rem;
    color: var(--text-light);
    margin: 0;
}

.account-balance {
    font-size: 1.25rem;
    font-weight: 600;
    margin: var(--spacing-sm) 0;
    color: var(--text-dark);
}

.account-number {
    font-size: 0.85rem;
    color: var(--text-light);
    margin: 0;
}

.account-actions {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-md);
}

.account-action-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.9rem;
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    transition: background-color 0.2s;
}

.account-action-btn:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.account-action-btn i {
    margin-right: var(--spacing-xs);
}

/* Form styles */
.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.625rem;
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    font-size: 0.875rem;
    color: var(--text-color);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Asset table styles */
.assets-table .asset-name {
    font-weight: 500;
}

.assets-table .asset-symbol {
    color: var(--text-light);
    font-size: 0.75rem;
}

.asset-class-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.asset-class-stock {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.asset-class-reit {
    background-color: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

.asset-class-crypto {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.asset-class-etf {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.asset-class-bond {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.asset-class-international {
    background-color: rgba(236, 72, 153, 0.1);
    color: #ec4899;
}

.asset-class-other {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.asset-change {
    font-weight: 500;
}

.asset-change.positive {
    color: #10b981;
}

.asset-change.negative {
    color: #ef4444;
}
