<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meu <PERSON>f<PERSON>lio | Dashboard Financeiro Global</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/new-layout.css">
    <link rel="stylesheet" href="css/open-finance.css">
    <link rel="stylesheet" href="css/professional-theme.css">
    <link rel="stylesheet" href="css/improved-layout.css">
    <link rel="stylesheet" href="css/portfolio-enhanced.css">
    <link rel="stylesheet" href="css/transactions.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar">
        <div class="sidebar-logo">
            <h1>RP Finance</h1>
        </div>
        <ul class="sidebar-menu">
            <li><a href="index.html"><i class="fas fa-chart-line"></i> Dashboard</a></li>
            <li><a href="best-assets.html"><i class="fas fa-star"></i> Melhores Ativos</a></li>
            <li><a href="news.html"><i class="fas fa-newspaper"></i> Notícias</a></li>
            <li><a href="portfolio.html" class="active"><i class="fas fa-briefcase"></i> Meu Portfólio</a></li>
            <li><a href="#" id="settings-link"><i class="fas fa-cog"></i> Configurações</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="dashboard-container">
        <!-- Header removed as requested -->

        <main class="main-content" style="width: 100%; display: block;">
            <!-- Indicador de carregamento -->
            <div id="loading-indicator" class="loading-overlay">
                <div class="loading-spinner"></div>
                <p>Carregando dados do portfólio...</p>
            </div>

            <!-- Resumo do Portfólio -->
            <div class="portfolio-summary-row">
                <div class="metric-card">
                    <div class="metric-title"><i class="fas fa-wallet"></i> Valor Total</div>
                    <div class="metric-value" id="portfolio-total-value">R$ 0,00</div>
                    <div class="metric-change" id="portfolio-total-change">0,00%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title"><i class="fas fa-chart-line"></i> Rentabilidade</div>
                    <div class="metric-value" id="portfolio-return">0,00%</div>
                    <div class="metric-period">Desde o início</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title"><i class="fas fa-coins"></i> Ativos</div>
                    <div class="metric-value" id="portfolio-assets-count">0</div>
                    <div class="metric-period">Em seu portfólio</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title"><i class="fas fa-balance-scale"></i> Diversificação</div>
                    <div class="metric-value" id="portfolio-diversification">0,00%</div>
                    <div class="metric-period">Índice de diversificação</div>
                </div>
            </div>

            <!-- Gráficos do Portfólio -->
            <div class="portfolio-charts-row">
                <div class="card chart-card">
                    <h2>Alocação por Classe</h2>
                    <div class="chart-container">
                        <canvas id="allocation-chart"></canvas>
                    </div>
                </div>
                <div class="card chart-card">
                    <h2>Desempenho do Portfólio</h2>
                    <div class="chart-container">
                        <canvas id="performance-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Open Finance Integration -->
            <div class="card open-finance-section">
                <div class="open-finance-header">
                    <h2><i class="fas fa-university"></i> Open Finance</h2>
                    <div class="open-finance-actions">
                        <button type="button" class="refresh-open-finance-btn" id="refresh-open-finance-btn" title="Atualizar conexões" onclick="if(window.portfolioManager) window.portfolioManager.refreshOpenFinance();">
                            <i class="fas fa-sync-alt"></i> Atualizar
                        </button>
                        <button type="button" class="connect-bank-btn" id="connect-bank-btn" onclick="if(window.portfolioManager) window.portfolioManager.connectBank();">
                            <i class="fas fa-link"></i>Conectar Banco
                        </button>
                        <button type="button" class="connect-rico-btn" id="header-connect-rico-btn" onclick="if(window.PluggyManager) window.PluggyManager.connectToRico();">
                            <i class="fas fa-chart-line"></i>Rico Investimentos
                        </button>
                    </div>
                </div>
                <div id="open-finance-content">
                    <div id="open-finance-empty" class="empty-accounts">
                        <i class="fas fa-university"></i>
                        <p>Nenhuma conta bancária conectada</p>
                        <p>Conecte suas contas bancárias para visualizar seus dados financeiros em um só lugar</p>
                        <div class="connect-buttons">
                            <button type="button" class="connect-bank-btn" id="empty-connect-btn" onclick="if(window.portfolioManager) window.portfolioManager.connectBank();">
                                <i class="fas fa-link"></i>Conectar Banco
                            </button>
                        </div>
                    </div>
                    <div id="connected-accounts" class="connected-accounts hidden">
                        <!-- Accounts will be added here dynamically -->
                    </div>
                </div>
            </div>

            <!-- Lista de Ativos -->
            <div class="card assets-card" style="width: 100%; overflow-x: auto;">
                <div class="card-header-actions">
                    <h2>Meus Ativos</h2>
                    <button type="button" class="btn btn-primary" id="add-asset-btn">
                        <i class="fas fa-plus btn-icon"></i>Adicionar Ativo
                    </button>
                </div>

                <div class="table-container" style="width: 100%; overflow-x: auto;">
                    <table id="assets-table">
                        <thead>
                            <tr>
                                <th class="sortable" data-sort="name">Ativo <i class="fas fa-sort"></i></th>
                                <th class="sortable" data-sort="assetClass">Classe <i class="fas fa-sort"></i></th>
                                <th class="sortable" data-sort="quantity">Quantidade <i class="fas fa-sort"></i></th>
                                <th class="sortable" data-sort="purchasePrice">Preço de Compra <i class="fas fa-sort"></i></th>
                                <th class="sortable" data-sort="currentPrice">Preço Atual <i class="fas fa-sort"></i></th>
                                <th class="sortable" data-sort="totalValue">Valor Total <i class="fas fa-sort"></i></th>
                                <th class="sortable" data-sort="portfolioPercentage">% do Portfolio <i class="fas fa-sort"></i></th>
                                <th class="sortable" data-sort="changePercent">Variação <i class="fas fa-sort"></i></th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="assets-table-body">
                            <!-- Conteúdo será preenchido via JavaScript -->
                            <tr class="empty-state">
                                <td colspan="9" class="empty-message">
                                    <div class="empty-content">
                                        <i class="fas fa-briefcase empty-icon"></i>
                                        <p>Seu portfólio está vazio</p>
                                        <p class="empty-description">Adicione ativos para começar a acompanhar seu desempenho</p>
                                        <button type="button" class="btn btn-primary" id="empty-add-btn">
                                            <i class="fas fa-plus btn-icon"></i>Adicionar Ativo
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>Dashboard criado com dados de múltiplas APIs financeiras | © 2025</p>
            <p>Atualização em tempo real ativada</p>
        </footer>
    </div>

    <!-- Modal para adicionar/editar ativo -->
    <div id="asset-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="asset-modal-title">Adicionar Ativo</h2>
                <button type="button" class="close-button" id="close-asset-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="asset-form">
                    <input type="hidden" id="asset-id">
                    <div class="form-group">
                        <label for="asset-symbol">Símbolo/Ticker</label>
                        <input type="text" id="asset-symbol" required placeholder="Ex: PETR4, AAPL, ITUB4">
                    </div>
                    <div class="form-group">
                        <label for="asset-name">Nome do Ativo</label>
                        <input type="text" id="asset-name" required placeholder="Ex: Petrobras, Apple, Itaú">
                    </div>
                    <div class="form-group">
                        <label for="asset-class">Classe de Ativo</label>
                        <select id="asset-class" required>
                            <option value="">Selecione uma classe</option>
                            <option value="stock">Ações</option>
                            <option value="reit">FIIs</option>
                            <option value="crypto">Criptomoedas</option>
                            <option value="etf">ETFs</option>
                            <option value="bond">Renda Fixa</option>
                            <option value="international">Ações Internacionais</option>
                            <option value="other">Outros</option>
                        </select>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="asset-quantity">Quantidade</label>
                            <input type="number" id="asset-quantity" required min="0.00001" step="0.00001" placeholder="Ex: 100">
                        </div>
                        <div class="form-group">
                            <label for="asset-price">Preço de Compra (R$)</label>
                            <input type="number" id="asset-price" required min="0.01" step="0.01" placeholder="Ex: 25.50">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="asset-date">Data de Compra</label>
                        <input type="date" id="asset-date" required>
                    </div>
                    <div class="form-group">
                        <label for="asset-notes">Notas (opcional)</label>
                        <textarea id="asset-notes" rows="2" placeholder="Adicione notas sobre este investimento"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="cancel-asset">Cancelar</button>
                <button type="button" class="btn btn-primary" id="save-asset">Salvar</button>
            </div>
        </div>
    </div>

    <!-- Modal de confirmação para excluir ativo -->
    <div id="confirm-delete-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Confirmar Exclusão</h2>
                <button type="button" class="close-button" id="close-confirm-modal">&times;</button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir o ativo <strong id="delete-asset-name"></strong>?</p>
                <p>Esta ação não pode ser desfeita.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="cancel-delete">Cancelar</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">Excluir</button>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Configurações</h2>
                <button type="button" class="close-button" id="close-settings">&times;</button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Aparência</h3>
                    <div class="setting-item">
                        <label for="theme-select">Tema</label>
                        <select id="theme-select">
                            <option value="light">Claro</option>
                            <option value="dark">Escuro</option>
                            <option value="system">Sistema</option>
                        </select>
                    </div>
                </div>
                <div class="settings-section">
                    <h3>Dados</h3>
                    <div class="setting-item">
                        <label for="refresh-interval">Intervalo de atualização</label>
                        <select id="refresh-interval">
                            <option value="30">30 segundos</option>
                            <option value="60">1 minuto</option>
                            <option value="300">5 minutos</option>
                            <option value="600">10 minutos</option>
                            <option value="1800">30 minutos</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="cache-duration">Duração do cache</label>
                        <select id="cache-duration">
                            <option value="300">5 minutos</option>
                            <option value="600">10 minutos</option>
                            <option value="1800">30 minutos</option>
                            <option value="3600">1 hora</option>
                        </select>
                    </div>
                </div>
                <div class="settings-section">
                    <h3>Preferências</h3>
                    <div class="setting-item checkbox-item">
                        <input type="checkbox" id="show-notifications" checked>
                        <label for="show-notifications">Mostrar notificações</label>
                    </div>
                    <div class="setting-item checkbox-item">
                        <input type="checkbox" id="auto-refresh" checked>
                        <label for="auto-refresh">Atualização automática</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="reset-settings">Restaurar padrões</button>
                <button type="button" class="btn btn-primary" id="save-settings">Salvar</button>
            </div>
        </div>
    </div>

    <!-- Transactions Modal (Extrato) -->
    <div id="transactions-modal" class="modal hidden">
        <div class="modal-content modal-lg">
            <div class="modal-header">
                <h2><i class="fas fa-list"></i> Extrato Bancário</h2>
                <button type="button" class="close-button" id="close-transactions-modal">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Account Info -->
                <div class="account-info-header">
                    <div class="account-info-name">
                        <i class="fas fa-university"></i> <span id="transaction-account-name">Conta</span>
                    </div>
                    <div class="account-info-balance">
                        Saldo: <span id="transaction-account-balance">R$ 0,00</span>
                    </div>
                </div>

                <!-- Filters -->
                <div class="transactions-filters">
                    <div class="filter-group">
                        <label for="transaction-period">Período:</label>
                        <select id="transaction-period" class="filter-select">
                            <option value="7">Últimos 7 dias</option>
                            <option value="15">Últimos 15 dias</option>
                            <option value="30" selected>Últimos 30 dias</option>
                            <option value="90">Últimos 3 meses</option>
                            <option value="180">Últimos 6 meses</option>
                            <option value="custom">Personalizado</option>
                        </select>
                    </div>

                    <div class="filter-group date-range hidden" id="custom-date-range">
                        <label for="transaction-start-date">De:</label>
                        <input type="date" id="transaction-start-date" class="filter-date">
                        <label for="transaction-end-date">Até:</label>
                        <input type="date" id="transaction-end-date" class="filter-date">
                    </div>

                    <div class="filter-group">
                        <label for="transaction-type">Tipo:</label>
                        <select id="transaction-type" class="filter-select">
                            <option value="all" selected>Todos</option>
                            <option value="CREDIT">Entradas</option>
                            <option value="DEBIT">Saídas</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <button type="button" class="btn btn-primary" id="apply-filters-btn">
                            <i class="fas fa-filter"></i> Filtrar
                        </button>
                    </div>
                </div>

                <!-- Search -->
                <div class="transactions-search">
                    <div class="search-input-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="transaction-search" placeholder="Buscar transações..." class="search-input">
                    </div>
                </div>

                <!-- Transactions Table -->
                <div class="transactions-table-container">
                    <table class="transactions-table" id="transactions-table">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Descrição</th>
                                <th>Categoria</th>
                                <th>Valor</th>
                                <th>Saldo</th>
                            </tr>
                        </thead>
                        <tbody id="transactions-table-body">
                            <!-- Transactions will be added here dynamically -->
                        </tbody>
                    </table>

                    <!-- Loading indicator -->
                    <div id="transactions-loading" class="transactions-loading hidden">
                        <div class="spinner"></div>
                        <p>Carregando transações...</p>
                    </div>

                    <!-- Empty state -->
                    <div id="transactions-empty" class="transactions-empty hidden">
                        <i class="fas fa-search"></i>
                        <p>Nenhuma transação encontrada</p>
                        <p>Tente ajustar os filtros ou período de busca</p>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="transactions-pagination" id="transactions-pagination">
                    <button type="button" class="pagination-btn" id="prev-page-btn" disabled>
                        <i class="fas fa-chevron-left"></i> Anterior
                    </button>
                    <div class="pagination-info">
                        Página <span id="current-page">1</span> de <span id="total-pages">1</span>
                    </div>
                    <button type="button" class="pagination-btn" id="next-page-btn" disabled>
                        Próxima <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="export-transactions-btn">
                    <i class="fas fa-file-export"></i> Exportar
                </button>
                <button type="button" class="btn btn-primary" id="close-transactions-btn">Fechar</button>
            </div>
        </div>
    </div>

    <!-- Fallback styles (in case CSS fails to load) -->
    <script src="js/fallback-styles.js"></script>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/config.js"></script>
    <script src="js/cache-manager.js"></script>
    <script src="js/api-manager.js"></script>
    <script src="js/api-integration.js"></script>
    <script src="js/user-preferences.js"></script>
    <script src="js/auth-manager.js"></script>
    <script src="js/data-loader.js"></script>

    <!-- Novos módulos de dados -->
    <script src="js/economic-loader.js"></script>
    <script src="js/stock-loader.js"></script>
    <script src="js/pluggy-manager.js"></script>

    <script src="js/charts.js"></script>
    <script src="js/portfolio.js"></script>
    <script src="js/realtime-updater.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
