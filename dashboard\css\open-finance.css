/* Open Finance Section */
.open-finance-section {
    margin-bottom: 30px;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
}

.open-finance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.open-finance-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.open-finance-header h2 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #334155;
}

.open-finance-header h2 i {
    color: #4a6cf7;
}

#open-finance-content {
    padding: 0;
}

/* Open Finance Summary */
.open-finance-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    background-color: rgba(74, 108, 247, 0.05);
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(74, 108, 247, 0.1);
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 10px;
}

.summary-item .summary-label {
    font-size: 0.85em;
    color: #666;
    margin-bottom: 5px;
}

.summary-item .summary-value {
    font-size: 1.5em;
    font-weight: 700;
    color: #333;
}

.summary-item .summary-change {
    font-size: 0.8em;
    margin-top: 3px;
}

.summary-item .summary-change.positive {
    color: #2ecc71;
}

.summary-item .summary-change.negative {
    color: #e74c3c;
}
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    background-color: rgba(74, 108, 247, 0.05);
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(74, 108, 247, 0.1);
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 10px;
}

.summary-item .summary-label {
    font-size: 0.85em;
    color: #666;
    margin-bottom: 5px;
}

.summary-item .summary-value {
    font-size: 1.5em;
    font-weight: 700;
    color: #333;
}

.summary-item .summary-change {
    font-size: 0.8em;
    margin-top: 3px;
}

.summary-item .summary-change.positive {
    color: #2ecc71;
}

.summary-item .summary-change.negative {
    color: #e74c3c;
}

.open-finance-accounts, .connected-accounts {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.account-card {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    width: calc(33.333% - 10px);
    min-width: 250px;
    position: relative;
    transition: transform 0.2s, box-shadow 0.2s;
    border-left: 4px solid #4a6cf7;
    display: flex;
    flex-direction: column;
}

.account-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.account-card.credit-card {
    border-left-color: #e74c3c;
}

.account-card.checking {
    border-left-color: #3498db;
}

.account-card.savings {
    border-left-color: #2ecc71;
}

.account-card.investment {
    border-left-color: #f39c12;
}

.account-card .account-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.account-card .account-type {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
    font-size: 1.1em;
}

.account-card .account-type i {
    margin-right: 8px;
    font-size: 1.2em;
}

.account-card.checking .account-type i {
    color: #3498db;
}

.account-card.credit-card .account-type i {
    color: #e74c3c;
}

.account-card.savings .account-type i {
    color: #2ecc71;
}

.account-card.investment .account-type i {
    color: #f39c12;
}

.account-card .account-institution {
    font-size: 0.85em;
    color: #777;
    margin-bottom: 15px;
}

.account-card .account-balance {
    font-size: 1.6em;
    font-weight: 700;
    margin-bottom: 5px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.account-card .account-balance .balance-trend {
    font-size: 0.6em;
    padding: 3px 6px;
    border-radius: 12px;
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
}

.account-card .account-balance .balance-trend.negative {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.account-card .account-number {
    color: #666;
    font-size: 0.9em;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.account-card .account-number i {
    margin-right: 5px;
    font-size: 0.9em;
    color: #888;
}

.account-card .account-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.85em;
    color: #777;
}

.account-card .account-meta .last-update {
    display: flex;
    align-items: center;
}

.account-card .account-meta .last-update i {
    margin-right: 5px;
}

.account-card .account-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
    padding-top: 15px;
}

.account-card .account-actions button {
    padding: 8px 14px;
    font-size: 0.85em;
    border: none;
    border-radius: 6px;
    background-color: #f5f5f5;
    color: #333;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

.account-card .account-actions button:hover {
    background-color: #e8e8e8;
    transform: translateY(-2px);
}

.account-card .account-actions button i {
    margin-right: 5px;
}

.account-card .refresh-btn {
    background: none;
    border: none;
    color: #4a6cf7;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.account-card .refresh-btn:hover {
    background-color: rgba(74, 108, 247, 0.1);
    transform: rotate(30deg);
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.account-card .refresh-btn.rotating {
    animation: rotate 1s linear infinite;
    background-color: rgba(74, 108, 247, 0.1);
}

.account-card .refresh-btn i {
    font-size: 1.1em;
}

.empty-accounts {
    background-color: #f9f9f9;
    border-radius: 12px;
    padding: 40px 30px;
    text-align: center;
    width: 100%;
    border: 1px dashed #ddd;
}

.empty-accounts i {
    font-size: 2.5em;
    color: #ccc;
    margin-bottom: 15px;
}

.empty-accounts p {
    color: #666;
    margin-bottom: 20px;
    font-size: 1.1em;
}

/* Connect buttons container */
.connect-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
    width: 100%;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

.connect-bank-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 18px;
    background-color: #4a6cf7;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(74, 108, 247, 0.3);
    width: 100%;
}

.connect-bank-btn:hover {
    background-color: #3a5ce5;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(74, 108, 247, 0.4);
}

.connect-bank-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(74, 108, 247, 0.3);
}

.connect-bank-btn i {
    margin-right: 8px;
}

/* Rico Investimentos button */
.connect-rico-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 18px;
    background-color: #7c3aed; /* Purple color for Rico */
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(124, 58, 237, 0.3);
    width: 100%;
}

/* Header Rico button should not be full width */
#header-connect-rico-btn {
    width: auto;
    background-color: #6d28d9; /* Slightly darker purple for the header button */
}

.connect-rico-btn:hover {
    background-color: #6d28d9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(124, 58, 237, 0.4);
}

.connect-rico-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(124, 58, 237, 0.3);
}

.connect-rico-btn i {
    margin-right: 8px;
}

.refresh-open-finance-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    background-color: #f8fafc;
    color: #4a6cf7;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.refresh-open-finance-btn:hover {
    background-color: #edf2f7;
    transform: translateY(-2px);
}

.refresh-open-finance-btn:active {
    transform: translateY(0);
}

.refresh-open-finance-btn i {
    margin-right: 8px;
}

.refresh-open-finance-btn.rotating i {
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.account-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 600;
    margin-left: 8px;
}

.account-badge.primary {
    background-color: rgba(52, 152, 219, 0.15);
    color: #3498db;
}

.account-badge.credit {
    background-color: rgba(231, 76, 60, 0.15);
    color: #e74c3c;
}

.account-badge.savings {
    background-color: rgba(46, 204, 113, 0.15);
    color: #2ecc71;
}

.account-badge.investment {
    background-color: rgba(243, 156, 18, 0.15);
    color: #f39c12;
}

/* Import Investments Button */
.import-investments-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 14px;
    background-color: #f39c12;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(243, 156, 18, 0.3);
    margin-top: 10px;
}

.import-investments-btn:hover {
    background-color: #e67e22;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.4);
}

.import-investments-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(243, 156, 18, 0.3);
}

.import-investments-btn i {
    margin-right: 8px;
}

/* Account Details Sections */
.account-details-section {
    margin-top: 15px;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.account-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    cursor: pointer;
}

.account-details-header h4 {
    font-size: 0.95em;
    font-weight: 600;
    color: #555;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 5px;
}

.account-details-header h4 i {
    font-size: 0.9em;
    color: #4a6cf7;
}

.account-details-content {
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease-out;
}

.account-details-content.expanded {
    max-height: 500px;
}

/* Credit Card Details */
.credit-details {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
}

.credit-detail-item {
    flex: 1;
    min-width: 100px;
    background-color: #f9f9f9;
    padding: 8px 12px;
    border-radius: 8px;
}

.credit-detail-item .detail-label {
    font-size: 0.75em;
    color: #777;
    margin-bottom: 3px;
}

.credit-detail-item .detail-value {
    font-size: 0.95em;
    font-weight: 600;
    color: #333;
}

/* Investment Details */
.investment-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.investment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.investment-item .investment-name {
    font-size: 0.9em;
    font-weight: 500;
    color: #333;
}

.investment-item .investment-type {
    font-size: 0.75em;
    color: #777;
}

.investment-item .investment-value {
    font-size: 0.95em;
    font-weight: 600;
    color: #333;
}

.investment-item .investment-return {
    font-size: 0.8em;
    font-weight: 500;
}

.investment-item .investment-return.positive {
    color: #2ecc71;
}

.investment-item .investment-return.negative {
    color: #e74c3c;
}

/* Transaction Preview */
.transaction-preview {
    margin-top: 10px;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-item .transaction-info {
    display: flex;
    flex-direction: column;
}

.transaction-item .transaction-description {
    font-size: 0.9em;
    font-weight: 500;
    color: #333;
}

.transaction-item .transaction-date {
    font-size: 0.75em;
    color: #777;
}

.transaction-item .transaction-amount {
    font-size: 0.95em;
    font-weight: 600;
}

.transaction-item .transaction-amount.positive {
    color: #2ecc71;
}

.transaction-item .transaction-amount.negative {
    color: #e74c3c;
}

.view-all-link {
    display: block;
    text-align: center;
    margin-top: 10px;
    font-size: 0.85em;
    color: #4a6cf7;
    text-decoration: none;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.view-all-link:hover {
    background-color: rgba(74, 108, 247, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .account-card {
        width: 100%;
    }

    .open-finance-summary {
        grid-template-columns: repeat(2, 1fr);
    }
}
