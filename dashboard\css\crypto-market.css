/* Estilos para a seção de mercado de criptomoedas */

.crypto-market-card {
    margin-bottom: 20px;
}

.crypto-market-container {
    margin-top: 20px;
}

.crypto-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.crypto-card {
    background-color: var(--card-bg-secondary, #f8f9fa);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.crypto-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.crypto-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.crypto-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    border-radius: 50%;
}

.crypto-name {
    flex: 1;
}

.crypto-name h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.crypto-symbol {
    font-size: 0.8rem;
    color: var(--text-light, #6c757d);
}

.crypto-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 1.2rem;
    font-weight: 600;
}

.crypto-change {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.positive {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.negative {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.crypto-details {
    margin-bottom: 12px;
}

.crypto-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.crypto-detail .label {
    color: var(--text-light, #6c757d);
}

.crypto-chart {
    height: 40px;
    margin-top: 12px;
}

.crypto-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.crypto-table th,
.crypto-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.crypto-table th {
    font-weight: 600;
    color: var(--text-light, #6c757d);
    background-color: var(--card-bg-secondary, #f8f9fa);
}

.crypto-table tr:hover {
    background-color: var(--hover-bg, #f3f4f6);
}

.crypto-name-cell {
    display: flex;
    align-items: center;
}

.crypto-icon-small {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    border-radius: 50%;
}

.crypto-name-text {
    display: block;
    font-weight: 500;
}

.crypto-symbol-text {
    display: block;
    font-size: 0.8rem;
    color: var(--text-light, #6c757d);
}

.loading-message {
    text-align: center;
    padding: 20px;
    color: var(--text-light, #6c757d);
}

.crypto-view-toggle {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
}

.view-button {
    background-color: var(--card-bg-secondary, #f8f9fa);
    border: 1px solid var(--border-color, #e5e7eb);
    padding: 6px 12px;
    margin-left: 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.view-button.active {
    background-color: var(--primary-color, #3b82f6);
    color: white;
    border-color: var(--primary-color, #3b82f6);
}

/* Responsividade */
@media (max-width: 768px) {
    .crypto-grid {
        grid-template-columns: 1fr;
    }
    
    .crypto-table th,
    .crypto-table td {
        padding: 8px 12px;
    }
    
    .crypto-table th:nth-child(5),
    .crypto-table td:nth-child(5) {
        display: none;
    }
}
