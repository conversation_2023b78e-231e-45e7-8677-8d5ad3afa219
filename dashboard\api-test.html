<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de APIs - Dashboard Financeiro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
            color: #1f2937;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        h1 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link i {
            margin-right: 5px;
        }
        
        .api-info {
            margin-bottom: 30px;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .api-info h2 {
            font-size: 1.4rem;
            margin-top: 0;
            margin-bottom: 15px;
            color: #111827;
        }
        
        .api-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .api-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .api-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .api-card h3 {
            font-size: 1.2rem;
            margin-top: 0;
            margin-bottom: 10px;
            color: #111827;
        }
        
        .api-card p {
            margin: 0 0 15px;
            color: #4b5563;
        }
        
        .api-card .tag {
            display: inline-block;
            background-color: #e5e7eb;
            color: #4b5563;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        .api-card .tag.premium {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .api-card .tag.free {
            background-color: #dcfce7;
            color: #166534;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Teste de APIs - Dashboard Financeiro</h1>
            <a href="index.html" class="back-link"><i class="fas fa-arrow-left"></i> Voltar ao Dashboard</a>
        </header>
        
        <div class="api-info">
            <h2>Informações sobre as APIs</h2>
            <p>Esta página permite testar as novas integrações de API adicionadas ao dashboard financeiro. Clique nos botões abaixo para testar cada API e ver os resultados.</p>
            <p>As APIs foram configuradas com as chaves fornecidas e estão prontas para uso no dashboard.</p>
        </div>
        
        <div class="api-list">
            <div class="api-card">
                <h3>Finnhub</h3>
                <p>API para dados de ações globais, índices, forex e análises.</p>
                <div class="tags">
                    <span class="tag premium">Premium</span>
                    <span class="tag">Ações</span>
                    <span class="tag">Índices</span>
                    <span class="tag">Forex</span>
                </div>
            </div>
            
            <div class="api-card">
                <h3>brapi.dev</h3>
                <p>API para dados de ações brasileiras, FIIs, BDRs e criptomoedas.</p>
                <div class="tags">
                    <span class="tag premium">PRO</span>
                    <span class="tag">Ações BR</span>
                    <span class="tag">FIIs</span>
                    <span class="tag">BDRs</span>
                </div>
            </div>
            
            <div class="api-card">
                <h3>CoinGecko</h3>
                <p>API para dados de criptomoedas globais e nacionais.</p>
                <div class="tags">
                    <span class="tag free">Free</span>
                    <span class="tag">Criptomoedas</span>
                    <span class="tag">Mercado</span>
                </div>
            </div>
            
            <div class="api-card">
                <h3>FRED</h3>
                <p>API para indicadores econômicos dos EUA e globais.</p>
                <div class="tags">
                    <span class="tag free">Free</span>
                    <span class="tag">Economia</span>
                    <span class="tag">Indicadores</span>
                </div>
            </div>
            
            <div class="api-card">
                <h3>GNews</h3>
                <p>API para notícias econômicas e financeiras.</p>
                <div class="tags">
                    <span class="tag free">Free</span>
                    <span class="tag">Notícias</span>
                    <span class="tag">Economia</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="js/api-test.js"></script>
</body>
</html>
