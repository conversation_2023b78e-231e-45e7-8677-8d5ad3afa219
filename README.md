# Dashboard Financeiro Global

Um dashboard interativo completo para gestão e visualização de dados financeiros, incluindo portfolio pessoal, Open Finance, índices globais, ações, criptomoedas e notícias do mercado.

## Funcionalidades

### 📊 **Dashboard Principal**
- Visualização de índices globais (Ibovespa, S&P 500, NASDAQ, etc.)
- Análise de ações brasileiras e internacionais
- Acompanhamento de criptomoedas (Bitcoin, Ethereum, etc.)
- Dados econômicos e indicadores financeiros

### 💼 **Portfolio Pessoal**
- Gestão completa de ativos (ações, criptos, FIIs, etc.)
- Cálculo automático de performance e métricas
- Gráficos de alocação e evolução do portfolio
- Integração com dados reais de preços via APIs

### 🏦 **Open Finance**
- Conexão segura com bancos via Pluggy API
- Visualização de contas bancárias e saldos
- Histórico de transações detalhado
- Importação automática de investimentos do Rico

### 📰 **Notícias Financeiras**
- Feed de notícias em tempo real via GNews API
- Filtros por tópicos financeiros
- Interface otimizada para leitura

### 🔄 **Recursos Avançados**
- Atualização automática de dados
- Sistema de cache inteligente
- Interface responsiva e moderna
- Tema profissional com modo escuro

## Configuração

### Pré-requisitos

- Node.js (versão 14 ou superior)
- NPM (gerenciador de pacotes do Node.js)

### Instalação

1. Clone este repositório:
   ```
   git clone <url-do-repositorio>
   cd dashboard-financeiro
   ```

2. Instale as dependências:
   ```
   npm install
   ```

3. As chaves de API já estão configuradas:
   - **Finnhub**: Para dados de ações globais
   - **Brapi.dev**: Para ações brasileiras
   - **CoinGecko**: Para criptomoedas
   - **GNews**: Para notícias financeiras
   - **FRED**: Para dados econômicos
   - **Pluggy**: Para Open Finance (credenciais já configuradas)

### Executando o projeto

1. Inicie o servidor proxy:
   ```
   npm start
   ```

2. Acesse o dashboard no navegador:
   ```
   http://localhost:3000
   ```

## Estrutura do Projeto

```
dashboard/                    # Frontend
├── index.html               # Dashboard principal
├── portfolio.html           # Gestão de portfolio
├── best-assets.html         # Melhores ativos
├── news.html               # Notícias financeiras
├── css/                    # Estilos
│   ├── styles.css          # Estilos principais
│   ├── professional-theme.css  # Tema profissional
│   ├── portfolio-enhanced.css  # Estilos do portfolio
│   └── open-finance.css    # Estilos Open Finance
└── js/                     # Scripts
    ├── config.js           # Configurações e APIs
    ├── dashboard.js        # Lógica principal
    ├── portfolio.js        # Gestão de portfolio
    ├── pluggy-manager.js   # Integração Pluggy
    ├── news.js            # Sistema de notícias
    └── best-assets.js     # Análise de ativos

proxy-server.js              # Servidor proxy principal
package.json                # Configuração Node.js
```

## APIs Integradas

### 📈 **Dados Financeiros**
- **Finnhub** - Ações globais e cotações em tempo real
- **Brapi.dev** - Ações brasileiras e dados do mercado nacional
- **CoinGecko** - Criptomoedas e dados de blockchain
- **FRED** - Indicadores econômicos do Federal Reserve

### 📰 **Notícias**
- **GNews** - Notícias financeiras globais em tempo real

### 🏦 **Open Finance**
- **Pluggy** - Conexão segura com bancos e instituições financeiras

## Solução de Problemas

### Problemas de CORS

O projeto inclui um servidor proxy para contornar problemas de CORS ao fazer chamadas diretas às APIs. Certifique-se de que o servidor proxy esteja em execução antes de acessar o dashboard.

### Limites de API

As APIs gratuitas têm limites de requisições. Se você encontrar erros relacionados a limites excedidos, o dashboard usará dados simulados como fallback.

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo LICENSE para detalhes.
