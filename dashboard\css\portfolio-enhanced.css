/* Enhanced Portfolio Styling */

/* Portfolio Summary Cards */
.portfolio-summary-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.metric-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-top: 4px solid var(--primary-color);
    display: flex;
    flex-direction: column;
}

.metric-card:nth-child(1) {
    border-top-color: var(--primary-color);
}

.metric-card:nth-child(2) {
    border-top-color: var(--positive-color);
}

.metric-card:nth-child(3) {
    border-top-color: var(--accent-color);
}

.metric-card:nth-child(4) {
    border-top-color: var(--neutral-color);
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.metric-title {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.metric-title i {
    margin-right: 0.5rem;
    color: var(--accent-color);
    font-size: 1rem;
}

.metric-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.metric-change, .metric-period {
    font-size: 0.875rem;
    color: var(--text-light);
}

.metric-change.positive {
    color: var(--positive-color);
}

.metric-change.negative {
    color: var(--negative-color);
}

/* Portfolio Charts */
.portfolio-charts-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.chart-card h2 {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.chart-card h2::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 1em;
    background-color: var(--accent-color);
    margin-right: 0.75rem;
    border-radius: 2px;
}

/* Open Finance Section */
.open-finance-section {
    margin-bottom: 1.5rem;
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.open-finance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.open-finance-header h2 {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.open-finance-header h2 i {
    margin-right: 0.75rem;
    color: var(--accent-color);
}

.connect-bank-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.connect-bank-btn i {
    margin-right: 0.5rem;
}

.connect-bank-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Empty Accounts State */
.empty-accounts {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-light);
}

.empty-accounts i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}

.empty-accounts p {
    margin-bottom: 0.5rem;
}

.empty-accounts p:first-of-type {
    font-weight: 500;
    font-size: 1.125rem;
    color: var(--text-color);
}

.empty-accounts button {
    margin-top: 1.5rem;
}

/* Connected Accounts */
.connected-accounts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.account-card {
    background-color: rgba(245, 247, 250, 0.5);
    border-radius: 8px;
    padding: 1.25rem;
    border-left: 4px solid var(--primary-color);
}

.account-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.bank-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin-right: 1rem;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.account-name {
    font-weight: 600;
    color: var(--text-dark);
}

.account-number {
    font-size: 0.875rem;
    color: var(--text-light);
}

.account-balance {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 1rem 0;
}

.account-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--text-light);
}

/* Assets Table */
.assets-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.card-header-actions h2 {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.card-header-actions h2::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 1em;
    background-color: var(--accent-color);
    margin-right: 0.75rem;
    border-radius: 2px;
}

#add-asset-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

#add-asset-btn i {
    margin-right: 0.5rem;
}

#add-asset-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Empty State */
.empty-state td {
    padding: 3rem 1rem;
}

.empty-content {
    text-align: center;
    color: var(--text-light);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}

.empty-message p {
    margin-bottom: 0.5rem;
}

.empty-message p:first-of-type {
    font-weight: 500;
    font-size: 1.125rem;
    color: var(--text-color);
}

.empty-description {
    margin-bottom: 1.5rem;
}

/* Modal Styling */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    transition: color 0.2s ease;
}

.close-button:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    gap: 1rem;
}

/* Form Styling */
.form-group {
    margin-bottom: 1.25rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

input[type="text"],
input[type="number"],
input[type="date"],
select,
textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
select:focus,
textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(15, 52, 96, 0.1);
    outline: none;
}

/* Button Styling */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-outline {
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-danger {
    background-color: var(--negative-color);
    color: white;
    border: none;
}

.btn-danger:hover {
    background-color: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-icon {
    margin-right: 0.5rem;
}

/* Settings Modal */
.settings-section {
    margin-bottom: 2rem;
}

.settings-section h3 {
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.setting-item {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}

.checkbox-item {
    flex-direction: row;
    align-items: center;
}

.checkbox-item input {
    margin-right: 0.75rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .portfolio-summary-row,
    .portfolio-charts-row,
    .connected-accounts {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
    }
}
