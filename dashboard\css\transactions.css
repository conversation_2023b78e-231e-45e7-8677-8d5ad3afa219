/* Transactions Modal Styles */

/* Modal size for transactions */
.modal-lg {
    width: 90%;
    max-width: 1200px;
}

/* Account info header */
.account-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: var(--card-bg);
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.account-info-name {
    font-size: 1.2rem;
    font-weight: 600;
}

.account-info-name i {
    margin-right: 8px;
    color: var(--primary-color);
}

.account-info-balance {
    font-size: 1.2rem;
    font-weight: 600;
}

/* Filters */
.transactions-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 0.9rem;
}

.filter-date {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 0.9rem;
}

.date-range {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Search */
.transactions-search {
    margin-bottom: 20px;
}

.search-input-container {
    position: relative;
    width: 100%;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 1rem;
}

/* Transactions Table */
.transactions-table-container {
    position: relative;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
}

.transactions-table th {
    position: sticky;
    top: 0;
    background-color: var(--card-header-bg);
    color: var(--text-color);
    text-align: left;
    padding: 12px 15px;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
}

.transactions-table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
}

.transactions-table tbody tr:hover {
    background-color: var(--hover-bg);
}

.transaction-date {
    white-space: nowrap;
    font-size: 0.9rem;
}

.transaction-description {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.transaction-category {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    background-color: var(--tag-bg);
    color: var(--text-color);
}

.transaction-amount {
    font-weight: 600;
    text-align: right;
    white-space: nowrap;
}

.transaction-amount.positive {
    color: var(--success-color);
}

.transaction-amount.negative {
    color: var(--danger-color);
}

.transaction-balance {
    font-weight: 500;
    text-align: right;
    white-space: nowrap;
}

/* Date headers */
.date-header {
    background-color: var(--card-bg);
    padding: 8px 15px;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

/* Loading indicator */
.transactions-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Empty state */
.transactions-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: var(--text-light);
}

.transactions-empty i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.transactions-empty p {
    margin: 5px 0;
}

.transactions-empty p:first-of-type {
    font-weight: 600;
    font-size: 1.1rem;
}

/* Pagination */
.transactions-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-btn {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--button-bg);
    color: var(--text-color);
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--button-hover-bg);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 0.9rem;
}

/* Dark mode adjustments */
.dark-theme .spinner {
    border-color: rgba(255, 255, 255, 0.1);
    border-top-color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .transactions-filters {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-select, .filter-date {
        width: 100%;
    }
    
    .date-range {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .transactions-table th, 
    .transactions-table td {
        padding: 10px 8px;
    }
    
    .transaction-description {
        max-width: 150px;
    }
}
