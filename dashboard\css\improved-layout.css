/* Improved Layout CSS */

/* Enhanced Grid Layout */
.main-content {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem;
    width: 100%;
    padding: 1.5rem;
}

/* Card Layouts */
.summary-card {
    grid-column: span 12;
}

.indices-card {
    grid-column: span 8;
}

.regions-card {
    grid-column: span 4;
}

.sectors-card, .stocks-card {
    grid-column: span 6;
}

.correlations-card, .best-assets-card, .category-performance-card, .asset-comparison-card, .featured-news-card, .sentiment-card, .filter-card {
    grid-column: span 12;
}

/* Card Styling */
.card {
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    overflow: hidden;
    border: none;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card h2 {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

/* Enhanced Table Styling */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 1rem;
}

th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem;
    text-align: left;
}

th:first-child {
    border-top-left-radius: 8px;
}

th:last-child {
    border-top-right-radius: 8px;
}

td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

tr:last-child td {
    border-bottom: none;
}

tr:last-child td:first-child {
    border-bottom-left-radius: 8px;
}

tr:last-child td:last-child {
    border-bottom-right-radius: 8px;
}

tr:hover td {
    background-color: rgba(225, 177, 106, 0.05);
}

/* Enhanced Chart Styling */
.chart-container {
    background-color: rgba(245, 247, 250, 0.5);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Enhanced Summary Grid */
.summary-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
}

.summary-item {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-left: 4px solid var(--primary-color);
    display: flex;
    flex-direction: column;
}

.summary-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.summary-label {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.summary-label i {
    margin-right: 0.5rem;
    color: var(--accent-color);
}

.summary-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
}

.summary-detail {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-top: auto;
}

/* Enhanced Filter Styling */
.filter-container {
    background-color: rgba(245, 247, 250, 0.5);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.filter-container h3 {
    color: var(--primary-color);
    font-size: 1rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.filter-button {
    padding: 0.6rem 1.25rem;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    cursor: pointer;
}

.filter-button:hover {
    background-color: rgba(15, 52, 96, 0.05);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.filter-button.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Enhanced Sidebar */
.sidebar {
    background: linear-gradient(to bottom, var(--primary-dark), var(--primary-color));
    box-shadow: var(--sidebar-shadow);
}

.sidebar-logo {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo h1 {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
}

.sidebar-menu a {
    padding: 1rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    border-left: 4px solid transparent;
    transition: all 0.2s ease;
}

.sidebar-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: var(--accent-color);
}

.sidebar-menu a.active {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--accent-color);
    border-left-color: var(--accent-color);
    font-weight: 500;
}

.sidebar-menu i {
    margin-right: 1rem;
    font-size: 1.25rem;
    width: 1.5rem;
    text-align: center;
    color: var(--accent-light);
}

/* Enhanced Footer */
.footer {
    background-color: var(--card-bg);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
    margin-top: 2rem;
    color: var(--text-light);
    font-size: 0.875rem;
    display: flex;
    justify-content: space-between;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
}

/* Enhanced Loading Indicator */
.loading-overlay {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    text-align: center;
}

.loading-spinner {
    border: 4px solid rgba(15, 52, 96, 0.1);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin: 0 auto 1rem;
}

/* Enhanced Error Message */
.error-message {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    text-align: center;
    border-top: 4px solid var(--negative-color);
}

.error-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.retry-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    margin-top: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.retry-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .indices-card, .regions-card, .sectors-card, .stocks-card {
        grid-column: span 12;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
        gap: 1rem;
    }
    
    .card {
        padding: 1rem;
    }
    
    .summary-content {
        grid-template-columns: 1fr;
    }
    
    .button-group {
        flex-wrap: wrap;
    }
    
    .filter-button {
        flex: 1 0 calc(50% - 0.5rem);
    }
}
