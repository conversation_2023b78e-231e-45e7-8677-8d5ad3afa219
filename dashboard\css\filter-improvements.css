/* Filter Improvements CSS */

/* Filter Card */
.filter-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    margin-top: 0.5rem; /* Added margin-top since header was removed */
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.filter-card:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.filter-card h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: rgba(59, 130, 246, 0.05);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#active-filters-count {
    font-size: 0.8rem;
    font-weight: 500;
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    opacity: 0.9;
    margin-left: 0.5rem;
}

/* Filter Container */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    padding: 1.25rem;
    gap: 2rem;
    background: linear-gradient(to bottom, rgba(59, 130, 246, 0.02), transparent);
}

/* Filter Sections */
.topic-filter, .language-filter {
    flex: 1;
    min-width: 250px;
}

.topic-filter h3, .language-filter h3 {
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-dark);
    display: flex;
    align-items: center;
}

.topic-filter h3::before {
    content: '🏷️';
    margin-right: 0.5rem;
    font-size: 1rem;
}

.language-filter h3::before {
    content: '🌐';
    margin-right: 0.5rem;
    font-size: 1rem;
}

/* Button Groups */
.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Filter Buttons */
.filter-button {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--text-color);
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.filter-button:hover {
    background-color: rgba(59, 130, 246, 0.05);
    border-color: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-button.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.filter-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Button ripple effect */
.filter-button::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.4s, opacity 0.8s;
}

.filter-button:active::after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
}

/* Topic-specific buttons */
.filter-button[data-topic="stocks"]::before {
    content: '📈';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.filter-button[data-topic="crypto"]::before {
    content: '₿';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.filter-button[data-topic="reits"]::before {
    content: '🏢';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.filter-button[data-topic="fixed-income"]::before {
    content: '💵';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.filter-button[data-topic="etfs"]::before {
    content: '📊';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.filter-button[data-topic="gold"]::before {
    content: '🥇';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.filter-button[data-topic="economy"]::before {
    content: '🏛️';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.filter-button[data-topic="all"]::before {
    content: '🔍';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

/* Language-specific buttons */
.filter-button[data-language="pt"]::before {
    content: '🇧🇷';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.filter-button[data-language="en"]::before {
    content: '🇺🇸';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.filter-button[data-language="all"]::before {
    content: '🌎';
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
        gap: 1.5rem;
    }

    .topic-filter, .language-filter {
        width: 100%;
    }
}
