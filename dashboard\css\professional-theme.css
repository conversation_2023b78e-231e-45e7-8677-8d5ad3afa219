/* Professional Finance Theme CSS */

/* Color Variables */
:root {
    /* Primary Colors - Navy and Gold */
    --primary-color: #0F3460;
    --primary-light: #1A5F7A;
    --primary-dark: #051C30;
    --accent-color: #E1B16A;
    --accent-light: #F5D6A8;
    --accent-dark: #C89A4F;

    /* Background Colors */
    --bg-color: #F5F7FA;
    --card-bg: #FFFFFF;
    --sidebar-bg: #0F3460;
    --header-bg: #FFFFFF;

    /* Text Colors */
    --text-color: #2D3748;
    --text-light: #718096;
    --text-dark: #1A202C;
    --text-white: #FFFFFF;
    --text-accent: #E1B16A;

    /* Status Colors */
    --positive-color: #10B981;
    --negative-color: #EF4444;
    --neutral-color: #3B82F6;

    /* Border Colors */
    --border-color: #E2E8F0;
    --border-accent: #E1B16A;

    /* Shadows */
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    --sidebar-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    --header-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Base Styles */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    font-family: 'Inter', sans-serif;
}

/* Sidebar Styling */
.sidebar {
    background-color: var(--sidebar-bg);
    box-shadow: var(--sidebar-shadow);
}

.sidebar-logo h1 {
    color: var(--text-white);
}

.sidebar-menu a {
    color: rgba(255, 255, 255, 0.8);
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.sidebar-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
    border-left-color: var(--accent-color);
}

.sidebar-menu a.active {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--accent-color);
    border-left-color: var(--accent-color);
    font-weight: 500;
}

.sidebar-menu i {
    color: var(--accent-light);
}

/* Card Styling */
.card {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    border: none;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.12);
}

.card h2 {
    color: var(--primary-color);
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
}

/* Table Styling */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
}

th {
    background-color: var(--primary-color);
    color: var(--text-white);
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem;
}

td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

tr:last-child td {
    border-bottom: none;
}

tr:hover td {
    background-color: rgba(225, 177, 106, 0.05);
}

/* Button Styling */
.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
    border: none;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-outline {
    border: 1px solid var(--border-color);
    color: var(--primary-color);
    background-color: transparent;
    transition: all 0.2s ease;
}

.btn-outline:hover {
    border-color: var(--primary-color);
    background-color: rgba(15, 52, 96, 0.05);
}

/* Chart Styling */
.chart-container {
    padding: 1rem;
    background-color: rgba(245, 247, 250, 0.5);
    border-radius: 8px;
    margin: 1.5rem 0;
}

/* Metrics Card Styling */
.metric-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-top: 3px solid var(--primary-color);
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.metric-title {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.metric-title i {
    color: var(--accent-color);
}

.metric-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.metric-change.positive {
    color: var(--positive-color);
    font-weight: 500;
}

.metric-change.negative {
    color: var(--negative-color);
    font-weight: 500;
}

/* Value Classes */
.positive-value {
    color: var(--positive-color);
    font-weight: 500;
}

.negative-value {
    color: var(--negative-color);
    font-weight: 500;
}

.neutral-value {
    color: var(--neutral-color);
    font-weight: 500;
}

/* Footer Styling */
.footer {
    color: var(--text-light);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 0;
    margin-top: 2rem;
}

/* Loading Indicator */
.loading-spinner {
    border-color: var(--primary-color);
    border-top-color: var(--accent-color);
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
    .sidebar {
        width: 220px;
    }
    
    .dashboard-container {
        margin-left: 220px;
        max-width: calc(100% - 220px);
    }
}

/* Improved Grid Layout */
.main-content {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem;
    width: 100%;
}

.summary-card {
    grid-column: span 12;
}

.indices-card {
    grid-column: span 8;
}

.regions-card {
    grid-column: span 4;
}

.sectors-card {
    grid-column: span 6;
}

.stocks-card {
    grid-column: span 6;
}

.correlations-card {
    grid-column: span 12;
}

/* Improved Summary Grid */
.summary-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
}

.summary-item {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.25rem;
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-left: 3px solid var(--primary-color);
}

.summary-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.summary-label {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.summary-detail {
    font-size: 0.875rem;
    color: var(--text-light);
}

/* Tab Styling */
.tab-button {
    padding: 0.75rem 1.25rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    color: var(--text-light);
    background-color: transparent;
}

.tab-button:hover {
    background-color: rgba(15, 52, 96, 0.05);
    color: var(--primary-color);
}

.tab-button.active {
    background-color: var(--primary-color);
    color: var(--text-white);
}

/* Stock Item Styling */
.stock-item {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 1.25rem;
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-left: 3px solid var(--primary-color);
    margin-bottom: 1rem;
}

.stock-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stock-symbol {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.125rem;
}

.stock-name {
    color: var(--text-light);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.stock-price {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.stock-return-day,
.stock-return-month,
.stock-return-year {
    font-weight: 500;
}

/* Badge Styling */
.badge {
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
}

.badge-primary {
    background-color: rgba(15, 52, 96, 0.1);
    color: var(--primary-color);
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--positive-color);
}

.badge-warning {
    background-color: rgba(225, 177, 106, 0.1);
    color: var(--accent-color);
}

.badge-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--negative-color);
}
