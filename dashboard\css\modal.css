/* Estilos para modais */

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s;
}

.modal-content {
    position: relative;
    background-color: var(--card-bg, #ffffff);
    margin: 5% auto;
    padding: 0;
    width: 90%;
    max-width: 1200px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-primary, #1f2937);
}

.close-modal {
    color: var(--text-light, #6b7280);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.close-modal:hover {
    color: var(--text-primary, #1f2937);
}

.modal-body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

/* Estilos para a tabela de criptomoedas */
.crypto-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

.crypto-table th,
.crypto-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.crypto-table th {
    font-weight: 600;
    color: var(--text-light, #6b7280);
    background-color: var(--card-bg-secondary, #f9fafb);
}

.crypto-table tr:hover {
    background-color: var(--hover-bg, #f3f4f6);
}

.crypto-name-cell {
    display: flex;
    align-items: center;
}

.crypto-icon-small {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    border-radius: 50%;
}

.crypto-name-text {
    display: block;
    font-weight: 500;
}

.crypto-symbol-text {
    display: block;
    font-size: 0.8rem;
    color: var(--text-light, #6b7280);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Responsividade */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .crypto-table {
        font-size: 0.85rem;
    }
    
    .crypto-table th,
    .crypto-table td {
        padding: 8px 12px;
    }
    
    .crypto-name-text {
        font-size: 0.9rem;
    }
    
    .crypto-symbol-text {
        font-size: 0.75rem;
    }
}
